# 📋 MÓDULO DE AUDITORIA E LOGS

## 📋 VISÃO GERAL

### **🎯 OBJETIVOS:**
- Registrar todas as ações dos usuários no sistema
- Garantir integridade e imutabilidade dos logs
- Fornecer acesso exclusivo ao admin para logs detalhados
- Implementar rastreabilidade completa de alterações
- Monitorar segurança e detectar anomalias
- Cumprir requisitos de conformidade e auditoria

### **📊 RESPONSABILIDADES:**
- Log detalhado de todas as ações de usuários
- Auditoria de alterações em dados críticos
- Monitoramento de segurança e tentativas de acesso
- Relatórios de auditoria para conformidade
- Detecção de anomalias e atividades suspeitas
- Backup e arquivamento de logs

---

## 🚀 FUNCIONALIDADES

### **✅ FUNCIONALIDADES PRINCIPAIS:**

#### **1. AUDITORIA COMPLETA:**
- Log de todas as ações CRUD
- Rastreamento de alterações (before/after)
- Registro de tentativas de acesso
- Monitoramento de sessões

#### **2. SEGURANÇA E INTEGRIDADE:**
- Hash SHA-256 para integridade (blockchain interno)
- Logs verdadeiramente imutáveis (append-only + rules)
- Criptografia AES-256 de dados sensíveis
- Detecção automática de tentativas de manipulação
- Assinatura digital de logs críticos
- Backup imutável em storage separado

#### **3. ACESSO CONTROLADO:**
- Apenas admin_total pode acessar logs detalhados
- Logs filtrados por nível de acesso
- Relatórios sanitizados para outros usuários
- Auditoria de acesso aos próprios logs

#### **4. ANÁLISE E RELATÓRIOS:**
- Dashboard de atividades em tempo real
- Relatórios de conformidade (LGPD, CLT)
- Detecção de padrões anômalos com IA
- Alertas automáticos de segurança
- Métricas de performance do sistema

#### **5. POLÍTICA DE RETENÇÃO LGPD:**
- **Logs de Acesso**: 6 meses (Art. 37 LGPD)
- **Logs de Alteração**: 5 anos (Art. 74 CLT)
- **Logs de Segurança**: 2 anos (Marco Civil)
- **Logs de Auditoria**: 10 anos (Código Civil)
- **Dados Pessoais**: Conforme finalidade + 1 ano
- **Purga Automática**: Executada mensalmente
- **Anonimização**: Após período de retenção

---

## 🔧 IMPLEMENTAÇÃO TÉCNICA DA IMUTABILIDADE

### **1. ESTRUTURA DE BLOCKCHAIN INTERNO:**

```sql
-- Tabela principal de logs (append-only)
CREATE TABLE logs_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bloco_anterior_hash VARCHAR(64), -- Hash do bloco anterior
  timestamp TIMESTAMP DEFAULT NOW(),
  usuario_id UUID,
  acao VARCHAR(100) NOT NULL,
  tabela_afetada VARCHAR(100),
  registro_id UUID,
  dados_anteriores JSONB,
  dados_novos JSONB,
  ip_origem INET,
  user_agent TEXT,
  hash_proprio VARCHAR(64) NOT NULL, -- SHA-256 do próprio registro
  assinatura_digital TEXT, -- Assinatura RSA para logs críticos
  CONSTRAINT no_update_allowed CHECK (false) -- Impede UPDATEs
);

-- Regras para tornar verdadeiramente imutável
CREATE OR REPLACE RULE no_update_logs AS ON UPDATE TO logs_auditoria DO INSTEAD NOTHING;
CREATE OR REPLACE RULE no_delete_logs AS ON DELETE TO logs_auditoria DO INSTEAD NOTHING;

-- Trigger para calcular hash automaticamente
CREATE OR REPLACE FUNCTION calcular_hash_log() RETURNS TRIGGER AS $$
DECLARE
  hash_anterior VARCHAR(64);
  dados_completos TEXT;
BEGIN
  -- Buscar hash do último registro
  SELECT hash_proprio INTO hash_anterior
  FROM logs_auditoria
  ORDER BY timestamp DESC
  LIMIT 1;

  -- Concatenar todos os dados para hash
  dados_completos := CONCAT(
    COALESCE(hash_anterior, ''),
    NEW.timestamp::TEXT,
    COALESCE(NEW.usuario_id::TEXT, ''),
    NEW.acao,
    COALESCE(NEW.tabela_afetada, ''),
    COALESCE(NEW.registro_id::TEXT, ''),
    COALESCE(NEW.dados_anteriores::TEXT, ''),
    COALESCE(NEW.dados_novos::TEXT, ''),
    COALESCE(NEW.ip_origem::TEXT, ''),
    COALESCE(NEW.user_agent, '')
  );

  -- Calcular hash SHA-256
  NEW.bloco_anterior_hash := hash_anterior;
  NEW.hash_proprio := encode(digest(dados_completos, 'sha256'), 'hex');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_calcular_hash
  BEFORE INSERT ON logs_auditoria
  FOR EACH ROW EXECUTE FUNCTION calcular_hash_log();
```

### **2. VALIDAÇÃO DE INTEGRIDADE:**

```typescript
// Função para validar integridade da cadeia
async function validarIntegridadeLogs(): Promise<boolean> {
  const logs = await db.logs_auditoria.findMany({
    orderBy: { timestamp: 'asc' }
  });

  for (let i = 1; i < logs.length; i++) {
    const logAtual = logs[i];
    const logAnterior = logs[i - 1];

    // Verificar se hash anterior está correto
    if (logAtual.bloco_anterior_hash !== logAnterior.hash_proprio) {
      console.error(`Integridade violada no log ${logAtual.id}`);
      return false;
    }

    // Recalcular hash e verificar
    const hashCalculado = calcularHashLog(logAtual);
    if (hashCalculado !== logAtual.hash_proprio) {
      console.error(`Hash inválido no log ${logAtual.id}`);
      return false;
    }
  }

  return true;
}
```

### **3. POLÍTICA DE BACKUP IMUTÁVEL:**

```typescript
// Backup automático para storage imutável
const backupConfig = {
  destino: 'aws-s3-glacier', // Storage imutável
  frequencia: 'diario',
  retencao: {
    logs_acesso: '6_meses',
    logs_alteracao: '5_anos',
    logs_seguranca: '2_anos',
    logs_auditoria: '10_anos'
  },
  criptografia: 'AES-256',
  compressao: 'gzip'
};
```

---

## 📱 TELAS E INTERFACES

### **1. DASHBOARD DE AUDITORIA** (`/auditoria`) - **ADMIN ONLY**

```typescript
interface DashboardAuditoriaScreen {
  acesso_restrito: {
    nivel_requerido: "admin_total";
    verificacao_adicional: "2FA obrigatório";
    log_acesso: "Todos os acessos são registrados";
  };
  
  resumo_atividades: {
    ultimas_24h: {
      total_acoes: number;
      usuarios_ativos: number;
      tentativas_login: number;
      falhas_autenticacao: number;
      alteracoes_criticas: number;
      acessos_suspeitos: number;
    };
    
    tendencias_semana: {
      atividade_por_dia: {
        data: Date;
        total_acoes: number;
        usuarios_unicos: number;
        alertas_seguranca: number;
      }[];
      
      usuarios_mais_ativos: {
        usuario: string;
        total_acoes: number;
        ultima_atividade: Date;
        nivel_acesso: string;
      }[];
      
      modulos_mais_usados: {
        modulo: string;
        total_acoes: number;
        tipos_acao: { [acao: string]: number };
      }[];
    };
  };
  
  alertas_seguranca: {
    criticos: {
      tipo: "tentativa_acesso_negado" | "alteracao_sem_permissao" | "login_suspeito";
      usuario: string;
      timestamp: Date;
      detalhes: string;
      ip_origem: string;
      acao_tomada: string;
    }[];
    
    avisos: {
      tipo: "multiplos_logins" | "acesso_horario_incomum" | "alteracao_massa";
      usuario: string;
      timestamp: Date;
      detalhes: string;
      requer_investigacao: boolean;
    }[];
  };
  
  integridade_logs: {
    status_geral: "integro" | "verificando" | "anomalia_detectada";
    ultima_verificacao: Date;
    total_logs_verificados: number;
    logs_com_anomalia: number;
    
    verificacao_hash: {
      logs_periodo: DateRange;
      hash_calculado: string;
      hash_esperado: string;
      status: "ok" | "divergencia";
    };
  };
  
  estatisticas_sistema: {
    armazenamento_logs: {
      tamanho_total_gb: number;
      crescimento_mensal_gb: number;
      retencao_configurada_meses: number;
      projecao_6_meses_gb: number;
    };
    
    performance_auditoria: {
      tempo_medio_log_ms: number;
      logs_por_segundo: number;
      fila_processamento: number;
      latencia_p95_ms: number;
    };
  };
}
```

### **2. LOGS DETALHADOS** (`/auditoria/logs`) - **ADMIN ONLY**

```typescript
interface LogsDetalhadosScreen {
  filtros_avancados: {
    periodo: {
      tipo: "hoje" | "ontem" | "7_dias" | "30_dias" | "personalizado";
      data_inicio: Date;
      data_fim: Date;
    };
    
    usuario: {
      busca: string;
      nivel_acesso: NivelAcesso[];
      empresa: UUID[];
      apenas_ativos: boolean;
    };
    
    acao: {
      modulo: "funcionarios" | "empresas" | "ponto" | "relatorios" | "biometria" | "configuracoes";
      tipo_acao: "create" | "read" | "update" | "delete" | "login" | "logout";
      resultado: "sucesso" | "erro" | "negado";
    };
    
    contexto: {
      ip_origem: string;
      user_agent: string;
      sessao_id: string;
      request_id: string;
    };
    
    dados: {
      incluir_dados_antes: boolean;
      incluir_dados_depois: boolean;
      apenas_alteracoes_criticas: boolean;
      mascarar_dados_sensiveis: boolean;
    };
  };
  
  visualizacao_logs: {
    modo_exibicao: "tabela" | "timeline" | "json";
    
    colunas_tabela: [
      "timestamp",
      "usuario",
      "acao",
      "modulo",
      "resultado",
      "ip_origem",
      "detalhes",
      "integridade"
    ];
    
    detalhes_log: {
      id_log: UUID;
      timestamp: Date;
      usuario: {
        nome: string;
        email: string;
        nivel_acesso: string;
        empresa: string;
      };
      acao: {
        modulo: string;
        tipo: string;
        descricao: string;
        endpoint?: string;
        metodo?: string;
      };
      contexto: {
        ip_origem: string;
        user_agent: string;
        sessao_id: string;
        request_id: string;
        referrer?: string;
      };
      dados_alteracao: {
        entidade_tipo: string;
        entidade_id: UUID;
        campos_alterados: string[];
        valores_antes: object;
        valores_depois: object;
      };
      resultado: {
        status: "sucesso" | "erro" | "negado";
        codigo_erro?: string;
        mensagem_erro?: string;
        tempo_execucao_ms: number;
      };
      integridade: {
        hash_sha256: string;
        verificado: boolean;
        timestamp_verificacao: Date;
      };
    };
  };
  
  acoes_logs: {
    exportar: {
      formato: "CSV" | "JSON" | "PDF";
      incluir_dados_sensiveis: boolean;
      periodo_maximo: "30 dias";
      assinatura_digital: boolean;
    };
    
    investigacao: {
      marcar_para_investigacao: boolean;
      adicionar_nota: string;
      notificar_equipe: string[];
      criar_caso_seguranca: boolean;
    };
    
    verificacao_integridade: {
      verificar_hash_individual: boolean;
      verificar_sequencia: boolean;
      gerar_relatorio_integridade: boolean;
    };
  };
  
  timeline_usuario: {
    usuario_selecionado: Usuario;
    periodo_analise: DateRange;
    
    linha_tempo: {
      timestamp: Date;
      acao: string;
      modulo: string;
      resultado: string;
      ip_origem: string;
      detalhes_resumidos: string;
      marcadores_especiais: "login" | "logout" | "erro" | "critico";
    }[];
    
    padroes_detectados: {
      horarios_acesso: { hora: number; frequencia: number }[];
      ips_frequentes: { ip: string; quantidade: number; localizacao?: string }[];
      acoes_frequentes: { acao: string; quantidade: number }[];
      anomalias: { tipo: string; descricao: string; timestamp: Date }[];
    };
  };
}
```

### **3. RELATÓRIOS DE CONFORMIDADE** (`/auditoria/relatorios`) - **ADMIN ONLY**

```typescript
interface RelatoriosConformidadeScreen {
  tipos_relatorio: {
    lgpd_compliance: {
      nome: "Relatório LGPD";
      descricao: "Acessos e alterações de dados pessoais";
      periodo_padrao: "mensal";
      campos_incluidos: [
        "Acessos a dados pessoais",
        "Alterações em dados sensíveis",
        "Tentativas de acesso negadas",
        "Exportações de dados",
        "Exclusões de dados"
      ];
    };
    
    auditoria_trabalhista: {
      nome: "Auditoria Trabalhista";
      descricao: "Registros de ponto e alterações";
      periodo_padrao: "mensal";
      campos_incluidos: [
        "Alterações em registros de ponto",
        "Justificativas aplicadas",
        "Relatórios gerados",
        "Configurações de jornada alteradas"
      ];
    };
    
    seguranca_sistema: {
      nome: "Relatório de Segurança";
      descricao: "Tentativas de acesso e anomalias";
      periodo_padrao: "semanal";
      campos_incluidos: [
        "Tentativas de login falhadas",
        "Acessos de IPs suspeitos",
        "Alterações em configurações críticas",
        "Uso de privilégios administrativos"
      ];
    };
    
    atividade_usuarios: {
      nome: "Atividade de Usuários";
      descricao: "Resumo de ações por usuário";
      periodo_padrao: "mensal";
      campos_incluidos: [
        "Usuários mais ativos",
        "Horários de maior atividade",
        "Módulos mais utilizados",
        "Padrões de uso anômalos"
      ];
    };
  };
  
  gerador_relatorio: {
    configuracao: {
      tipo_relatorio: string;
      periodo: {
        inicio: Date;
        fim: Date;
        max_periodo: "12 meses";
      };
      filtros: {
        usuarios_especificos: UUID[];
        modulos_especificos: string[];
        apenas_alteracoes_criticas: boolean;
        incluir_dados_anonimizados: boolean;
      };
      formato_saida: "PDF" | "Excel" | "CSV";
      incluir_graficos: boolean;
      assinatura_digital: boolean;
    };
    
    preview_conteudo: {
      resumo_executivo: string;
      total_registros: number;
      periodo_analisado: string;
      principais_achados: string[];
      recomendacoes: string[];
    };
    
    opcoes_entrega: {
      download_imediato: boolean;
      enviar_email: {
        destinatarios: string[];
        assunto_personalizado: string;
        mensagem: string;
      };
      agendar_recorrente: {
        frequencia: "semanal" | "mensal" | "trimestral";
        dia_envio: number;
        destinatarios: string[];
      };
    };
  };
  
  historico_relatorios: {
    relatorios_gerados: {
      data_geracao: Date;
      tipo: string;
      periodo_analisado: string;
      gerado_por: string;
      tamanho_arquivo: string;
      hash_integridade: string;
      status: "disponivel" | "arquivado" | "expirado";
      download_url?: string;
    }[];
    
    acoes_historico: {
      reexecutar_relatorio: boolean;
      comparar_periodos: boolean;
      arquivar_relatorio: boolean;
      verificar_integridade: boolean;
    };
  };
}
```

### **4. CONFIGURAÇÕES DE AUDITORIA** (`/auditoria/configuracoes`) - **ADMIN ONLY**

```typescript
interface ConfiguracoesAuditoriaScreen {
  politicas_log: {
    nivel_detalhamento: {
      minimo: {
        descricao: "Apenas ações críticas";
        incluir: ["login", "logout", "alteracoes_criticas"];
        impacto_performance: "baixo";
        espaco_disco: "baixo";
      };
      
      padrao: {
        descricao: "Todas as ações CRUD";
        incluir: ["todas_acoes_crud", "tentativas_acesso", "configuracoes"];
        impacto_performance: "medio";
        espaco_disco: "medio";
      };
      
      completo: {
        descricao: "Log detalhado de tudo";
        incluir: ["todas_acoes", "dados_antes_depois", "contexto_completo"];
        impacto_performance: "alto";
        espaco_disco: "alto";
      };
    };
    
    retencao_dados: {
      logs_sistema: {
        periodo_retencao_meses: number;
        min: 12;
        max: 120;
        padrao: 24;
      };
      
      logs_seguranca: {
        periodo_retencao_meses: number;
        min: 24;
        max: 120;
        padrao: 60;
      };
      
      logs_conformidade: {
        periodo_retencao_meses: number;
        min: 60;
        max: 120;
        padrao: 84; // 7 anos
      };
      
      arquivamento_automatico: {
        ativo: boolean;
        compressao: boolean;
        local_arquivo: "local" | "nuvem" | "ambos";
      };
    };
  };
  
  seguranca_logs: {
    integridade: {
      verificacao_hash: {
        ativo: boolean;
        frequencia: "diaria" | "semanal";
        algoritmo: "SHA-256";
      };
      
      assinatura_digital: {
        ativo: boolean;
        certificado_digital: string;
        validar_cadeia: boolean;
      };
      
      backup_logs: {
        ativo: boolean;
        frequencia: "diaria" | "semanal";
        local_backup: "local" | "nuvem" | "ambos";
        criptografia: boolean;
      };
    };
    
    acesso_logs: {
      autenticacao_adicional: {
        two_factor_obrigatorio: boolean;
        sessao_timeout_minutos: number;
        ip_whitelist: string[];
      };
      
      auditoria_acesso_logs: {
        log_visualizacoes: boolean;
        log_exportacoes: boolean;
        log_buscas: boolean;
        notificar_acessos: boolean;
      };
    };
  };
  
  alertas_automaticos: {
    deteccao_anomalias: {
      login_horario_incomum: {
        ativo: boolean;
        horario_normal_inicio: Time;
        horario_normal_fim: Time;
        notificar: string[];
      };
      
      multiplos_logins_simultaneos: {
        ativo: boolean;
        limite_sessoes: number;
        janela_tempo_minutos: number;
        acao: "alertar" | "bloquear";
      };
      
      volume_acoes_anormal: {
        ativo: boolean;
        multiplicador_media: number; // ex: 3x a média
        periodo_analise_horas: number;
        notificar: string[];
      };
      
      tentativas_acesso_negado: {
        ativo: boolean;
        limite_tentativas: number;
        janela_tempo_minutos: number;
        acao: "alertar" | "bloquear_ip";
      };
    };
    
    conformidade: {
      acesso_dados_sensiveis: {
        ativo: boolean;
        notificar_imediatamente: boolean;
        destinatarios: string[];
      };
      
      alteracoes_massa: {
        ativo: boolean;
        limite_registros: number;
        notificar: string[];
        requer_aprovacao: boolean;
      };
      
      exportacao_dados: {
        ativo: boolean;
        limite_registros: number;
        notificar_dpo: boolean;
        log_detalhado: boolean;
      };
    };
  };
  
  integracao_externa: {
    siem_integration: {
      ativo: boolean;
      endpoint_siem: string;
      formato_envio: "JSON" | "CEF" | "LEEF";
      eventos_incluir: string[];
      frequencia_envio: "tempo_real" | "batch_horario";
    };
    
    compliance_tools: {
      lgpd_monitor: {
        ativo: boolean;
        api_endpoint: string;
        eventos_privacidade: boolean;
      };
      
      sox_compliance: {
        ativo: boolean;
        relatorios_automaticos: boolean;
        certificacao_digital: boolean;
      };
    };
  };
}
```

---

## 🌐 APIs

### **📡 ENDPOINTS (ADMIN ONLY):**

```typescript
interface AuditoriaLogsAPI {
  // ✅ CONSULTA DE LOGS
  "GET /api/auditoria/logs": {
    headers: {
      "X-Admin-Level": "admin_total";
      "X-2FA-Token": string;
    };
    query: {
      usuario_id?: UUID;
      modulo?: string;
      acao?: string;
      data_inicio?: Date;
      data_fim?: Date;
      ip_origem?: string;
      resultado?: "sucesso" | "erro" | "negado";
      limite?: number;
      offset?: number;
    };
    response: {
      logs: LogAuditoria[];
      total: number;
      integridade_verificada: boolean;
    };
  };
  
  "GET /api/auditoria/logs/[id]": {
    headers: {
      "X-Admin-Level": "admin_total";
    };
    response: LogAuditoriaDetalhado;
  };
  
  // ✅ RELATÓRIOS
  "POST /api/auditoria/relatorios/gerar": {
    headers: {
      "X-Admin-Level": "admin_total";
    };
    body: {
      tipo: "lgpd" | "trabalhista" | "seguranca" | "atividade";
      periodo: DateRange;
      filtros: FiltrosRelatorio;
      formato: "PDF" | "Excel" | "CSV";
      assinatura_digital: boolean;
    };
    response: {
      relatorio_id: UUID;
      url_download: string;
      hash_integridade: string;
      tempo_geracao: number;
    };
  };
  
  // ✅ INTEGRIDADE
  "POST /api/auditoria/verificar-integridade": {
    headers: {
      "X-Admin-Level": "admin_total";
    };
    body: {
      periodo?: DateRange;
      log_ids?: UUID[];
    };
    response: {
      total_verificados: number;
      integros: number;
      com_anomalia: number;
      detalhes_anomalias: AnomaliaIntegridade[];
    };
  };
  
  // ✅ ESTATÍSTICAS
  "GET /api/auditoria/estatisticas": {
    headers: {
      "X-Admin-Level": "admin_total";
    };
    query: {
      periodo?: "hoje" | "semana" | "mes";
    };
    response: {
      total_logs: number;
      usuarios_ativos: number;
      acoes_por_modulo: { [modulo: string]: number };
      tentativas_acesso_negado: number;
      alertas_seguranca: number;
    };
  };
  
  // ✅ CONFIGURAÇÕES
  "GET /api/auditoria/configuracoes": {
    headers: {
      "X-Admin-Level": "admin_total";
    };
    response: ConfiguracoesAuditoria;
  };
  
  "PUT /api/auditoria/configuracoes": {
    headers: {
      "X-Admin-Level": "admin_total";
    };
    body: Partial<ConfiguracoesAuditoria>;
    response: { message: "Configurações atualizadas" };
  };
}
```

---

## 🗄️ BANCO DE DADOS

### **📊 TABELAS:**

```sql
-- ✅ LOGS DE AUDITORIA
CREATE TABLE logs_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Timestamp (imutável)
  timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
  
  -- Usuário
  usuario_id UUID REFERENCES users(id),
  usuario_nome VARCHAR(255) NOT NULL,
  usuario_email VARCHAR(255) NOT NULL,
  nivel_acesso VARCHAR(50) NOT NULL,
  
  -- Ação
  modulo VARCHAR(100) NOT NULL,
  acao VARCHAR(100) NOT NULL,
  descricao_acao TEXT NOT NULL,
  
  -- Contexto da Request
  ip_origem INET NOT NULL,
  user_agent TEXT,
  sessao_id VARCHAR(100),
  request_id VARCHAR(100),
  endpoint VARCHAR(500),
  metodo_http VARCHAR(10),
  
  -- Entidade Afetada
  entidade_tipo VARCHAR(100),
  entidade_id UUID,
  
  -- Dados da Alteração (criptografados se sensíveis)
  dados_antes JSONB,
  dados_depois JSONB,
  campos_alterados TEXT[],
  
  -- Resultado
  resultado VARCHAR(20) NOT NULL, -- 'sucesso', 'erro', 'negado'
  codigo_erro VARCHAR(50),
  mensagem_erro TEXT,
  tempo_execucao_ms INTEGER,
  
  -- Integridade
  hash_sha256 VARCHAR(64) NOT NULL,
  hash_anterior VARCHAR(64), -- para chain de integridade
  
  -- Classificação
  criticidade VARCHAR(20) DEFAULT 'normal', -- 'baixa', 'normal', 'alta', 'critica'
  categoria VARCHAR(50), -- 'dados_pessoais', 'financeiro', 'seguranca', etc.
  
  -- Flags
  dados_sensiveis BOOLEAN DEFAULT FALSE,
  requer_investigacao BOOLEAN DEFAULT FALSE,
  
  CONSTRAINT chk_resultado CHECK (
    resultado IN ('sucesso', 'erro', 'negado')
  ),
  CONSTRAINT chk_criticidade CHECK (
    criticidade IN ('baixa', 'normal', 'alta', 'critica')
  )
);

-- ✅ ACESSOS AOS LOGS (meta-auditoria)
CREATE TABLE acessos_logs_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Quem acessou
  admin_usuario_id UUID NOT NULL REFERENCES users(id),
  admin_nome VARCHAR(255) NOT NULL,
  
  -- Quando e como
  timestamp_acesso TIMESTAMP DEFAULT NOW(),
  ip_origem INET NOT NULL,
  user_agent TEXT,
  
  -- O que foi acessado
  tipo_acesso VARCHAR(50) NOT NULL, -- 'visualizacao', 'busca', 'exportacao', 'relatorio'
  filtros_aplicados JSONB,
  logs_acessados UUID[], -- IDs dos logs visualizados
  
  -- Contexto
  motivo_acesso TEXT,
  caso_investigacao VARCHAR(100),
  
  -- Resultado
  registros_retornados INTEGER,
  tempo_consulta_ms INTEGER,
  
  CONSTRAINT chk_tipo_acesso CHECK (
    tipo_acesso IN ('visualizacao', 'busca', 'exportacao', 'relatorio', 'verificacao_integridade')
  )
);

-- ✅ ALERTAS DE AUDITORIA
CREATE TABLE alertas_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Alerta
  tipo_alerta VARCHAR(100) NOT NULL,
  gravidade VARCHAR(20) NOT NULL, -- 'info', 'aviso', 'critico'
  titulo VARCHAR(255) NOT NULL,
  descricao TEXT NOT NULL,
  
  -- Contexto
  usuario_relacionado UUID REFERENCES users(id),
  logs_relacionados UUID[],
  dados_contexto JSONB,
  
  -- Detecção
  detectado_em TIMESTAMP DEFAULT NOW(),
  regra_deteccao VARCHAR(255),
  confianca_percentual INTEGER, -- 0-100
  
  -- Status
  status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'investigando', 'resolvido', 'falso_positivo'
  investigado_por UUID REFERENCES users(id),
  data_resolucao TIMESTAMP,
  notas_investigacao TEXT,
  
  -- Ações
  acao_automatica_executada BOOLEAN DEFAULT FALSE,
  detalhes_acao JSONB,
  
  CONSTRAINT chk_gravidade_alerta CHECK (
    gravidade IN ('info', 'aviso', 'critico')
  ),
  CONSTRAINT chk_status_alerta CHECK (
    status IN ('novo', 'investigando', 'resolvido', 'falso_positivo')
  )
);

-- ✅ CONFIGURAÇÕES DE AUDITORIA
CREATE TABLE configuracoes_auditoria (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Configuração
  categoria VARCHAR(100) NOT NULL,
  chave VARCHAR(255) NOT NULL,
  valor JSONB NOT NULL,
  
  -- Metadados
  descricao TEXT,
  tipo_valor VARCHAR(50),
  valor_padrao JSONB,
  
  -- Controle
  editavel BOOLEAN DEFAULT TRUE,
  requer_reinicializacao BOOLEAN DEFAULT FALSE,
  
  -- Auditoria da configuração
  alterado_em TIMESTAMP DEFAULT NOW(),
  alterado_por UUID REFERENCES users(id),
  
  UNIQUE(categoria, chave)
);

-- ✅ RELATÓRIOS DE CONFORMIDADE
CREATE TABLE relatorios_conformidade (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Relatório
  tipo_relatorio VARCHAR(100) NOT NULL,
  nome VARCHAR(255) NOT NULL,
  periodo_inicio DATE NOT NULL,
  periodo_fim DATE NOT NULL,
  
  -- Geração
  gerado_em TIMESTAMP DEFAULT NOW(),
  gerado_por UUID NOT NULL REFERENCES users(id),
  tempo_geracao_segundos INTEGER,
  
  -- Arquivo
  formato VARCHAR(10) NOT NULL, -- 'PDF', 'Excel', 'CSV'
  tamanho_arquivo_bytes BIGINT,
  url_arquivo VARCHAR(500),
  hash_arquivo VARCHAR(64),
  assinatura_digital TEXT,
  
  -- Conteúdo
  total_registros_analisados INTEGER,
  resumo_executivo TEXT,
  principais_achados JSONB,
  recomendacoes JSONB,
  
  -- Status
  status VARCHAR(20) DEFAULT 'disponivel', -- 'disponivel', 'arquivado', 'expirado'
  data_expiracao DATE,
  
  CONSTRAINT chk_formato_relatorio CHECK (
    formato IN ('PDF', 'Excel', 'CSV')
  )
);

-- ✅ ÍNDICES OTIMIZADOS
CREATE INDEX idx_logs_auditoria_timestamp ON logs_auditoria(timestamp DESC);
CREATE INDEX idx_logs_auditoria_usuario ON logs_auditoria(usuario_id, timestamp DESC);
CREATE INDEX idx_logs_auditoria_modulo_acao ON logs_auditoria(modulo, acao);
CREATE INDEX idx_logs_auditoria_ip_origem ON logs_auditoria(ip_origem);
CREATE INDEX idx_logs_auditoria_resultado ON logs_auditoria(resultado);
CREATE INDEX idx_logs_auditoria_criticidade ON logs_auditoria(criticidade);
CREATE INDEX idx_logs_auditoria_hash ON logs_auditoria(hash_sha256);
CREATE INDEX idx_acessos_logs_admin ON acessos_logs_auditoria(admin_usuario_id, timestamp_acesso DESC);
CREATE INDEX idx_alertas_status ON alertas_auditoria(status, gravidade);
CREATE INDEX idx_alertas_detectado ON alertas_auditoria(detectado_em DESC);
CREATE INDEX idx_relatorios_tipo ON relatorios_conformidade(tipo_relatorio, gerado_em DESC);

-- ✅ PARTICIONAMENTO POR DATA (PostgreSQL)
-- Particionar logs_auditoria por mês para performance
CREATE TABLE logs_auditoria_y2024m01 PARTITION OF logs_auditoria
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
-- ... continuar para outros meses
```

---

## 🔒 REGRAS DE NEGÓCIO

### **🔐 ACESSO RESTRITO:**
1. **Apenas admin_total** pode acessar logs detalhados
2. **2FA obrigatório** para acesso aos logs
3. **Todos os acessos** aos logs são auditados
4. **Sessão limitada** a 30 minutos

### **🛡️ INTEGRIDADE:**
1. **Hash SHA-256** para cada log
2. **Chain de integridade** entre logs sequenciais
3. **Logs imutáveis** após criação
4. **Verificação automática** diária

### **📊 RETENÇÃO:**
1. **Logs sistema**: 24 meses mínimo
2. **Logs segurança**: 60 meses mínimo
3. **Logs conformidade**: 84 meses (7 anos)
4. **Arquivamento automático** com compressão

### **⚠️ ALERTAS:**
1. **Detecção automática** de anomalias
2. **Notificação imediata** para eventos críticos
3. **Investigação obrigatória** para alertas críticos
4. **Falsos positivos** marcados e aprendidos

---

## 🧪 CASOS DE TESTE

### **✅ TESTES DE AUDITORIA:**

```typescript
describe("Sistema de Auditoria", () => {
  test("Registrar ação de usuário", async () => {
    const usuario = await criarUsuario({ nivel: "gerente_rh" });
    
    await logarAcao({
      usuario_id: usuario.id,
      modulo: "funcionarios",
      acao: "create",
      entidade_id: "novo-funcionario-id"
    });
    
    const logs = await buscarLogs({
      usuario_id: usuario.id,
      modulo: "funcionarios"
    });
    
    expect(logs).toHaveLength(1);
    expect(logs[0].hash_sha256).toBeDefined();
  });
  
  test("Verificar integridade dos logs", async () => {
    const logs = await buscarLogs({ limite: 100 });
    
    const verificacao = await verificarIntegridade(logs);
    
    expect(verificacao.integros).toBe(logs.length);
    expect(verificacao.com_anomalia).toBe(0);
  });
});
```

### **✅ TESTES DE ACESSO:**

```typescript
describe("Controle de Acesso", () => {
  test("Bloquear acesso não-admin", async () => {
    const usuario = await criarUsuario({ nivel: "gerente_rh" });
    
    await expect(acessarLogs(usuario))
      .rejects.toThrow("Acesso negado");
  });
  
  test("Permitir acesso admin com 2FA", async () => {
    const admin = await criarUsuario({ nivel: "admin_total" });
    const token2FA = await gerar2FA(admin);
    
    const logs = await acessarLogs(admin, { token2FA });
    
    expect(logs).toBeDefined();
    
    // Verificar se o acesso foi auditado
    const acessoLog = await buscarAcessosLogs({
      admin_usuario_id: admin.id
    });
    expect(acessoLog).toHaveLength(1);
  });
});
```

---

## 🚀 IMPLEMENTAÇÃO

### **📅 CRONOGRAMA:**

**Semana 1-2: Estrutura Base**
- Modelo de dados
- Sistema de hash/integridade
- APIs básicas

**Semana 3-4: Interface Admin**
- Dashboard de auditoria
- Visualização de logs
- Filtros avançados

**Semana 5-6: Relatórios**
- Gerador de relatórios
- Templates de conformidade
- Assinatura digital

**Semana 7-8: Alertas e Monitoramento**
- Detecção de anomalias
- Sistema de alertas
- Integrações externas

### **🔧 DEPENDÊNCIAS:**
- Sistema de autenticação (2FA)
- Criptografia (SHA-256, assinatura digital)
- Sistema de arquivos (relatórios)
- Notificações (alertas)
- Backup automático
- Particionamento de banco
